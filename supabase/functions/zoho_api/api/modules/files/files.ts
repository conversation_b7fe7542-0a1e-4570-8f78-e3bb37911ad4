

export interface ZohoFileUploadResponse {
  success: boolean;
  data?: {
    file_id: string;
    file_name: string;
  }[];
  error?: string;
}

export class FilesService {
  constructor() {
    // No dependencies needed for file processing
  }

  /**
   * Generate a file ID hash from file content
   */
  private async generateFileId(file: File): Promise<string> {
    // Create a hash from file name, size, and timestamp
    const fileInfo = `${file.name}_${file.size}_${Date.now()}`;
    const encoder = new TextEncoder();
    const data = encoder.encode(fileInfo);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
  }

  /**
   * Upload a file to Zoho CRM using the attachments API
   */
  public async uploadFileToZoho(file: File): Promise<{ success: boolean; data?: { file_id: string; file_name: string }; error?: string }> {

    try {
      // Import required modules

      const AccessTokenStore = (await import("../../../store.ts")).default;
      const { env } = await import("../../../../_shared/env.ts");


      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();

      const formData = new FormData();
      formData.append('file', file);

      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        // Don't set Content-Type for FormData, let the browser set it with boundary
      };

      // Try the files endpoint for file uploads
      const uploadUrl = `${env.ZOHO_API_URL}/crm/v2/files`;


      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: headers,
        body: formData,
      });

      console.log("Response status:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Zoho file upload error:", {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`File upload failed (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log("File upload result:", JSON.stringify(result, null, 2));

      // Extract file information from response
      if (result.data && result.data.length > 0) {
        const fileData = result.data[0];
        console.log("File data from Zoho:", JSON.stringify(fileData, null, 2));

        // Try different possible file ID fields
        const fileId = fileData.details?.id ||
                      fileData.id ||
                      fileData.file_id ||
                      fileData.details?.file_id ||
                      await this.generateFileId(file);

        return {
          success: true,
          data: {
            file_id: fileId,
            file_name: file.name
          }
        };
      } else {
        console.error("No file data in response:", result);
        throw new Error("No file data returned from Zoho");
      }

    } catch (error) {
      console.error("Error uploading file to Zoho:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to upload file to Zoho CRM"
      };
    }
  }

  /**
   * Process files for inclusion in Zoho record
   */
  public async processFilesForRecord(files: File[]): Promise<ZohoFileUploadResponse> {
    console.log(`\n📁 Processing ${files.length} files for Zoho record...`);

    try {
      const processedFiles: { file_id: string; file_name: string }[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`\n📄 Processing file ${i + 1}/${files.length}: ${file.name}`);

        const fileResult = await this.uploadFileToZoho(file);

        if (!fileResult.success) {
          console.error(`❌ Failed to upload file ${file.name}:`, fileResult.error);
          // Continue with other files instead of failing completely
          continue;
        }

        console.log(`✅ File uploaded successfully:`, fileResult.data);

        processedFiles.push({
          file_id: fileResult.data!.file_id,
          file_name: fileResult.data!.file_name
        });
      }

      console.log(`\n📋 Final processed files (${processedFiles.length}):`, processedFiles);

      return {
        success: true,
        data: processedFiles
      };

    } catch (error) {
      console.error("❌ Error processing files:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to process files"
      };
    }
  }

  /**
   * Process multiple files for Zoho CRM (using base64 encoding)
   */
  public async uploadFiles(files: File[]): Promise<ZohoFileUploadResponse> {
    return await this.processFilesForRecord(files);
  }

  /**
   * Format file data for Zoho record field
   * Converts uploaded file data to the format expected by Zoho file fields
   */
  public formatFilesForRecord(fileData: { file_id: string; file_name: string }[]): any[] {
    return fileData.map(file => ({
      file_id: file.file_id,
      file_name: file.file_name
    }));
  }
}
