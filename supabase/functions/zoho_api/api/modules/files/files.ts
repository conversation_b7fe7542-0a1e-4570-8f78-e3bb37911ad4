

export interface ZohoFileUploadResponse {
  success: boolean;
  data?: {
    attachment_id: string;
    file_name: string;
  }[];
  error?: string;
}

export interface ZohoAttachmentUploadResponse {
  success: boolean;
  data?: {
    attachment_id: string;
    file_name: string;
  };
  error?: string;
}

export class FilesService {
  constructor() {
    // No dependencies needed for file processing
  }

  /**
   * Upload a file as attachment to a Zoho CRM record using the attachments API
   */
  public async uploadAttachmentToRecord(
    file: File,
    recordId: string,
    moduleName: string = 'Subcontractors'
  ): Promise<ZohoAttachmentUploadResponse> {
    console.log(`\n🔄 Starting attachment upload: ${file.name} to ${moduleName} record ${recordId}`);
    console.log(`   File details: ${file.size} bytes, type: ${file.type}`);

    try {
      // Import required modules
      console.log("📦 Importing required modules...");
      const AccessTokenStore = (await import("../../../store.ts")).default;
      const { env } = await import("../../../../_shared/env.ts");

      console.log("🔑 Getting access token...");
      const tokenStore = AccessTokenStore.getInstance();
      const token = await tokenStore.getAccessToken();
      console.log("✅ Access token obtained:", token ? `${token.substring(0, 10)}...` : "null");

      // Create FormData for file upload
      console.log("📋 Creating FormData...");
      const formData = new FormData();
      formData.append('file', file);
      console.log("✅ FormData created with file:", file.name);

      const headers = {
        Authorization: `Zoho-oauthtoken ${token.trim()}`,
        // Don't set Content-Type for FormData, let the browser set it with boundary
      };
      console.log("📝 Headers prepared:", { Authorization: `Zoho-oauthtoken ${token.substring(0, 10)}...` });

      // Use the attachments endpoint for file uploads
      const uploadUrl = `${env.ZOHO_API_URL}/crm/v2/${moduleName}/${recordId}/Attachments`;
      console.log("🌐 Making request to:", uploadUrl);
      console.log("📤 Uploading attachment to Zoho:", file.name, "Size:", file.size);

      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: headers,
        body: formData,
      });

      console.log("Response status:", response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Zoho attachment upload error:", {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Attachment upload failed (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log("Attachment upload result:", JSON.stringify(result, null, 2));

      // Extract attachment information from response
      if (result.data && result.data.length > 0) {
        const attachmentData = result.data[0];
        console.log("Attachment data from Zoho:", JSON.stringify(attachmentData, null, 2));

        // Extract attachment ID from response
        const attachmentId = attachmentData.details?.id ||
                            attachmentData.id ||
                            attachmentData.attachment_id ||
                            attachmentData.details?.attachment_id;

        if (!attachmentId) {
          console.error("No attachment ID found in response:", attachmentData);
          throw new Error("No attachment ID returned from Zoho");
        }

        return {
          success: true,
          data: {
            attachment_id: attachmentId,
            file_name: file.name
          }
        };
      } else {
        console.error("No attachment data in response:", result);
        throw new Error("No attachment data returned from Zoho");
      }

    } catch (error) {
      console.error("Error uploading attachment to Zoho:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to upload attachment to Zoho CRM"
      };
    }
  }

  /**
   * Upload multiple attachments to a Zoho CRM record
   */
  public async uploadAttachmentsToRecord(
    files: File[],
    recordId: string,
    moduleName: string = 'Subcontractors'
  ): Promise<ZohoFileUploadResponse> {
    console.log(`\n📁 Processing ${files.length} attachments for ${moduleName} record ${recordId}...`);

    try {
      const processedFiles: { attachment_id: string; file_name: string }[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        console.log(`\n📄 Processing attachment ${i + 1}/${files.length}: ${file.name}`);

        const attachmentResult = await this.uploadAttachmentToRecord(file, recordId, moduleName);

        if (!attachmentResult.success) {
          console.error(`❌ Failed to upload attachment ${file.name}:`, attachmentResult.error);
          // Continue with other files instead of failing completely
          continue;
        }

        console.log(`✅ Attachment uploaded successfully:`, attachmentResult.data);

        processedFiles.push({
          attachment_id: attachmentResult.data!.attachment_id,
          file_name: attachmentResult.data!.file_name
        });
      }

      console.log(`\n📋 Final processed attachments (${processedFiles.length}):`, processedFiles);

      return {
        success: true,
        data: processedFiles
      };

    } catch (error) {
      console.error("❌ Error processing attachments:", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to process attachments"
      };
    }
  }

  /**
   * Format attachment data for Zoho record field
   * Converts uploaded attachment data to the format expected by Zoho file fields
   */
  public formatAttachmentsForRecord(attachmentData: { attachment_id: string; file_name: string }[]): any[] {
    return attachmentData.map(attachment => ({
      attachment_id: attachment.attachment_id,
      file_name: attachment.file_name
    }));
  }
}
