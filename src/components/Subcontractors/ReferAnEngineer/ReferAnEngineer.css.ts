import { style } from '@vanilla-extract/css';
import {theme} from "@/styles/themes.css";
import {breakpoints} from "@/styles/constants.css";

export const container = style({
  display: 'flex',
  borderRadius: '15px',
  fontFamily: theme.fonts.primary,
  '@media': {
    'screen and (max-width: 768px)': {
      flexDirection: 'column',
    },
  },
});

export const leftPanel = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,
  padding: '40px',
  borderRadius: '24px',
  marginRight: '48px',
  display: 'flex',
  flexDirection: 'column',
  minHeight: '700px',
  position: 'relative',
  overflow: 'hidden',
  '@media': {
    'screen and (max-width: 768px)': {
      width: '100%',
      marginRight: '0',
      marginBottom: '20px',
    },
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  fontSize: '40px',
  lineHeight: '95%',
  color: theme.colors.primary.softWhite,
  '@media': {
    [breakpoints.tablet]: {
      fontSize: '64px',
    }
  }
});

export const earn = style({
  color: theme.colors.primary.asidGreen,
  fontStyle: 'italic',
});

export const cash = style({
  color: theme.colors.primary.asidGreen,
});

export const subtitle = style({
  color: theme.colors.primary.softWhite,
  fontFamily: theme.fonts.secondary,
  fontSize: '20px',
  marginTop: '40px',
  fontWeight: 700,
  lineHeight: '120%',
});

export const description = style({
  fontFamily: theme.fonts.secondary,
  color: theme.colors.primary.softWhite,
  fontSize: '20px',
  marginTop: '20px',
  lineHeight: '120%',
  marginBottom: '18px',
});

export const image = style({
  width: '130%',
  height: '100%',
  borderRadius: '10px',
  marginTop: '-40px',
  position: "relative",
  top: "40px",
  right: "20px",
  objectFit: "cover",
  '@media': {
    [breakpoints.tablet]: {
      display: 'none',
    },
    [breakpoints.desktop]: {
      display: 'block',
      width: '100%',
      height: '100%',
      // marginTop: "1px",
      // marginTop: "-185px",
      // right: "50px",
      left: "40px",

    }
  }
});

export const rightPanel = style({
  width: '60%',
  '@media': {
    'screen and (max-width: 768px)': {
      width: '100%',
    },
  },
});

export const accordion = style({
  // Accordion styles
});

export const accordionItem = style({
  fontSize: '20px',
  backgroundColor: '#FFFDF8',
  padding: '20px',
  borderRadius: '24px',
  marginBottom: '20px',
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
});

export const accordionTitle = style({
  fontWeight: 700,
  display: 'flex',
  alignItems: 'center',
  fontSize: "20px",
});

export const titleWithDropdown = style({
  fontWeight: 700,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  fontSize: "20px",
  fontStyle: "normal",
  lineHeight: "120%"
});

export const titleDropDownIcon = style({
  cursor: 'pointer',
})

export const icon = style({
  marginRight: '14px',
  // Placeholder for icons
});

export const accordionToggleIcon = style({
  border: 'solid #00462A',
  borderWidth: '0 2px 2px 0',
  display: 'inline-block',
  padding: '3px',
  transform: 'rotate(45deg)',
  transition: 'transform 0.3s ease-in-out',
});

export const open = style({
  transform: 'rotate(-135deg)',
});

export const accordionContent = style({
  paddingTop: '10px',
});

export const highlightedParagraph = style({
  margin: '14px 0',
  fontStyle: "italic"
})

export const li = style({
  display: 'flex',
  lineHeight: "120%",
  marginBottom: "14px"
})

export const checkIcon = style({
  color: theme.colors.primary.castletonGreen,
  marginRight: '10px',
});

export const list = style({
  listStyleType: 'none',
  paddingLeft: '0',
  marginTop: '10px',
  lineHeight: '1.6',
});

export const total = style({
  color: "rgba(0, 61, 35, 0.50)"
});

export const formContainer = style({
  backgroundColor: '#FFFDF8',
  padding: '20px',
  borderRadius: '24px',
  marginTop: '15px',
  fontSize: "16px",
  fontFamily: theme.fonts.secondary,
});

export const form = style({
  marginTop: '16px'
});

export const label = style({
  display: 'block',
  marginBottom: '5px',
  fontWeight: 500,
});

export const inputGroup = style({
  display: 'flex',
});

export const input = style({
  width: '100%',
  height: "56px",
  padding: '10px',
  border: '1px solid #ccc',
  borderRadius: '5px 0 0 5px',
  fontSize: '16px',
});

export const button = style({
  backgroundColor: theme.colors.primary.asidGreen,
  marginLeft: '2px',
  border: 'none',
  padding: '10px 15px',
  borderRadius: '8px',
  cursor: 'pointer',
  fontSize: '24px',
  lineHeight: '1',
  height: '56px',
  width: '80px'
});