import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";

export const heroRoot = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  scrollSnapAlign: "start",
  position: "relative",
  margin: "8px 0 0 0",
  borderRadius: "24px",
  "@media": {
    [breakpoints.tablet]: {
      margin: "12px 0 0 0",
    }
  }
});

export const heroContainer = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  borderRadius: 16,
  padding: "30px 20px",
  display: "flex",
  flexDirection: "column",
  alignItems: "flex-start",
  color: theme.colors.primary.ivory,
  position: "relative",
  overflow: "hidden",

  "@media": {
    [breakpoints.tablet]: {
      borderRadius: 24,
      padding: "48px",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      minHeight: "500px",
    },
  },
});

export const textColumn = style({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  gap: 16,
  maxWidth: "100%",
  zIndex: 2,

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "992px",
      gap: 20,
    },
  },
});

export const title = style({
  fontFamily: theme.fonts.primary,
  fontSize: 32,
  lineHeight: "95%",
  fontWeight: 400,
  maxWidth: "100%",
  marginBottom: "10px",
  color: theme.colors.primary.ivory,

  "@media": {
    "(max-width: 767px)": {
      fontSize: 28,
      whiteSpace: "normal",
      wordWrap: "break-word",
      width: "100%",
    },
    [breakpoints.tablet]: {
      fontSize: 48,
    },
    [breakpoints.desktop]: {
      fontSize: 88,
      letterSpacing: "-2%",
    },
  },
});

export const consistent = style({
  fontWeight: 500,
  fontStyle: "italic",
})

export const regular = style({
  // fontStyle: "italic",
});

export const noAdmins = style({
  color: theme.colors.primary.asidGreen,
  fontWeight: 400,
});
export const bold500 = style({
  fontWeight: 500,
})
export const highlight = style({
  color: theme.colors.primary.asidGreen,
  fontStyle: "italic",
  fontWeight: 400,
});

export const subtitle = style({
  fontSize: 18,
  fontWeight: 500,
  letterSpacing: "-2%",
  lineHeight: "120%",
  marginBottom: "15px",
  marginTop: "10px",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: 24,
      lineHeight: "95%",
    }
  },
});

export const applyText = style({
  color: theme.colors.primary.asidGreen,
});

export const buttons = style({
  display: "flex",
  flexDirection: "column",
  gap: 24,
  width: "100%",
  marginTop: "20px",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      alignItems: "center",
      width: "auto",
      gap: 32,
    },
  },
});

export const joinButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  fontWeight: 600,
  fontSize: "20px",
  padding: "12px 24px",
  borderRadius: "30px",
  border: "none",
  cursor: "pointer",
  transition: "background-color 0.3s ease",

  ":hover": {
    backgroundColor: theme.colors.primary.asidGreen,
  },

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "18px",
      padding: "14px 28px",
    },
  },
});

export const contactButtons = style({
  display: "flex",
  gap: "16px",

  "@media": {
    [breakpoints.tablet]: {
      gap: "24px",
    },
  },
});

export const imageContainer = style({
  position: "relative",
  width: "100%",
  height: "300px",
  marginTop: "30px",

  "@media": {
    [breakpoints.tablet]: {
      position: "absolute",
      width: "50%",
      height: "96%",
      right: 0,
      top: 25,
      marginTop: 0,
    },
    "(max-width: 1075px)": {
      display: "none",
    },
  },
});

export const mobileOnly = style({
  display: "none",
  
  "@media": {
    "(max-width: 767px)": {
      display: "block",
      backgroundColor: theme.colors.primary.castletonGreen,
      borderRadius: 24,
      overflow: "hidden",
      margin: "8px 0 0 0",
    }
  }
});

export const mobileHeroContainer = style({
  display: "flex",
  flexDirection: "column",
  padding: "32px 24px 0",
  color: theme.colors.primary.ivory,
});

export const mobileTitle = style({
  fontFamily: theme.fonts.primary,
  fontSize: "44px",
  lineHeight: 1.2,
  fontWeight: 400,
  marginBottom: 16,
  color: theme.colors.primary.ivory,
});

export const mobileItalic = style({
  fontStyle: "italic",
  fontWeight: 500
});

export const mobileGreen = style({
  color: theme.colors.primary.asidGreen,
});

export const mobileGreenItalic = style({
  color: theme.colors.primary.asidGreen,
  fontStyle: "italic",
});

export const mobileSubtitle = style({
  fontSize: "18px",
  lineHeight: 1.2,
  marginBottom: 24,
  fontWeight: 500,
});

export const mobileButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  fontFamily: theme.fonts.secondary,
  width: "100%",
  borderRadius: 30,
  padding: "12px 24px",
  fontSize: 20,
  fontWeight: 600,
  marginBottom: 24,
  border: "none",
  cursor: "pointer",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
});

export const mobileImage = style({
  position: "relative",
  width: "100%",
  height: 300,
  marginTop: "-40px",
  pointerEvents: "none",
});

export const desktopOnly = style({
  "@media": {
    "(max-width: 767px)": {
      display: "none",
    }
  }
});

