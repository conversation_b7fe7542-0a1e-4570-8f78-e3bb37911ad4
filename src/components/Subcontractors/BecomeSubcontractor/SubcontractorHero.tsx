"use client";

import Container from "@components/Container";
import * as styles from "./SubcontractorHero.css";
import Image from "next/image";
import Typography from "@components/Typography";
import Button from "@components/Button";
import plumberImage from "@components/ProtectYourBusiness/components/ProtectYourBusiness/protect_business.png";
import Link from "next/link";

const SubcontractorHero = () => {
  return (
    <Container>
      {/* Mobile version - only shows on small screens */}
      <div className={styles.mobileOnly}>
        <div className={styles.mobileHeroContainer}>
          <Typography as="h1" className={styles.mobileTitle}>
            <span className={styles.mobileItalic}>Consistent</span><br />
            Work.<br />
            <span className={styles.mobileGreen}>No Admin.</span><br />
            No Hassle.<br />
            <span className={styles.mobileGreenItalic}><span className={styles.mobileItalic}>Fast</span> <br/> Payments.</span>
          </Typography>
          <Typography as="h2" className={styles.mobileSubtitle}>
            Your Next Job Is Already Waiting — <div className={styles.mobileGreen}>Apply to Become a Subcontractor</div>
          </Typography>
          <Button className={styles.mobileButton} as={Link} href={"/become-subcontractor"}>
            → Join Our Team
          </Button>
          <div className={styles.mobileImage}>
            <Image
              src={plumberImage.src}
              alt="Pleasant Plumbers Technician"
              fill
              objectFit="contain"
              objectPosition="bottom center"
              priority
            />
          </div>
        </div>
      </div>

      {/* Desktop version - unchanged */}
      <div className={styles.desktopOnly}>
        <div className={styles.heroRoot}>
          <div className={styles.heroContainer}>
            <div className={styles.textColumn}>
              <Typography variant="h1" className={styles.title}>
                <span className={styles.consistent}>Consistent</span> Work.<br />
                <span className={styles.noAdmins}>No Admin.</span><br />
                <span className={styles.regular}>No Hassle.</span><br />
                <span className={styles.highlight}><span className={styles.bold500}>Fast</span> Payments.</span>
              </Typography>
              <Typography variant="h2" className={styles.subtitle}>
                Your Next Job Is Already Waiting — <span className={styles.applyText}>Apply to Become a Subcontractor</span>
              </Typography>
              <div className={styles.buttons}>
                <Button
                  variant="filled"
                  className={styles.joinButton}
                  as={Link} href="/become-subcontractor"
                >
                  → Join Our Team
                </Button>
              </div>
            </div>
            <div className={styles.imageContainer}>
              <Image
                src={plumberImage.src}
                alt="Pleasant Plumbers Technician"
                fill
                objectFit="contain"
                objectPosition="center"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default SubcontractorHero;
